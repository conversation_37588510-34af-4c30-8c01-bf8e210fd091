<template>
	<div class="hfull flex flex-col gap-3">
		<!-- 顶部卡片统计 -->
		<div class="grid grid-cols-4 gap-3">
			<StatsCard icon="i-carbon:delivery-truck" label="总运单数" :value="total" tag-text="总计" />
			<StatsCard icon="i-carbon:car" label="在途车辆" :value="computeStatusCount(TransportOrderStatus.在途)" tag-text="在途" />
			<StatsCard icon="i-carbon:hourglass" label="待发车" :value="computeStatusCount(TransportOrderStatus.待发车)" tag-text="待发" />
			<StatsCard icon="i-carbon:checkmark" label="已送达" :value="computeStatusCount(TransportOrderStatus.已送达)" tag-text="完成" theme="green" />
		</div>

		<!-- 表格与分页区域 -->
		<div
			class="min-h-0 flex flex-1 flex-col overflow-hidden border border-blue-700/30 rounded-lg from-blue-900/20 to-blue-800/10 bg-gradient-to-br p-3"
		>
			<!-- 表格 -->
			<div class="min-h-0 flex-1 overflow-hidden">
				<x-table
					v-loading="loading"
					class="!hfull"
					:loading="loading"
					:data="list"
					:columns="columns"
					:ellipsis="true"
					:header-align="'center'"
					:body-align="'center'"
					:max-height="'100%'"
					col-class="p-xxs"
				/>
			</div>
			<!-- 分页 -->
			<div class="mt-3 flex justify-end">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import StatsCard from './StatsCard.vue'
import { useTransportOrderStore, TransportOrderStatus, VehicleOnlineStatus } from '@/stores/useTransportOrderStore'
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
function computeStatusCount(status) {
	return statsData.value?.byStatus?.[status] || 0
}
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	orderNo: null,
	powerPlantId: null,
	departureId: null,
	destinationId: null,
	vehicleId: null,
	driverId: null,
	licensePlate: null,
	driverPhone: null,
	cargoInfo: null,
	cargoWeight: null,
	startTime: null,
	deliveryTime: [],
	estimatedArrivalTime: [],
	actualArrivalTime: [],
	status: null,
	createTime: [],
})

// 监听 computedParams 的变化，同步更新 queryModel
watch(
	() => props.params,
	(newParams) => {
		Object.assign(queryModel, newParams)
	},
	{ deep: true, immediate: true },
)

const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmTransportOrder_APIS.getPage, queryModel)
const { loading: statsLoading, run: statsRun, data: statsData } = xUseRequest(BIZ_JnrmTransportOrder_APIS.getStatsByStatus, queryModel)

onMounted(() => {
	handleRun()
})
// 数据源
const transportOrderStore = useTransportOrderStore()

// 顶部卡片统计

// 列定义（x-table 支持点语法取嵌套字段，formatter 用于转换/格式化）
const columns = [
	{ label: '运单编号', prop: 'orderNo', width: 160 },
	{ label: '车牌号', prop: 'vehicle.licensePlate', width: 120 },
	{ label: '出发地', prop: 'departure.name', width: 160 },
	{ label: '目的地', prop: 'destination.name', width: 160 },
	{
		label: '状态',
		prop: 'status',
		width: 100,
		styles: {
			[TransportOrderStatus.在途]: '!text-blue-300',
			[TransportOrderStatus.待发车]: '!text-yellow-300',
			[TransportOrderStatus.已送达]: '!text-green-300',
			[TransportOrderStatus.异常]: '!text-red-300',
			[TransportOrderStatus.取消]: '!text-gray-400',
			default: '',
		},
	},
	{
		label: '在线状态',
		prop: 'vehicleOnlineStatus',
		width: 100,
		formatter: (value: any) => transportOrderStore.formatVehicleOnlineStatus(value),
		styles: {
			[VehicleOnlineStatus.ONLINE]: '!text-green-300',
			[VehicleOnlineStatus.OFFLINE]: '!text-gray-400',
			default: '',
		},
	},
	{ label: '出发时间', prop: 'startTime', width: 160, formatter: X_DATE_UTILS.formatDate },
	{ label: '预计到达', prop: 'estimateArriveTime', width: 160 },
]

async function handleRun() {
	await nextTick()
	statsRun()
	run()
}

defineExpose({ handleRun })
</script>

<style scoped></style>
